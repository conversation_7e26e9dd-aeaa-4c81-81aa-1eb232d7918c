#!/usr/bin/env bash

# 检查是否已经引用
if [ -z "$COMMON_SH_IMPORTED" ]; then
  # 设置引用标记
  COMMON_SH_IMPORTED="true"

  # 环境变量
  export LANG=en_US.UTF-8
  export LC_ALL=en_US.UTF-8

  # 运行常量
  readonly STATE_DIR='/sp_state'
  readonly RESPONSE_FDR='./response'
  readonly CONFIG_FDR='./configs'
  readonly REMOTE_FDR='转存合集'

  test -z "${STATE_DIR}"
  test -z "${RESPONSE_FDR}"
  test -z "${CONFIG_FDR}"
  test -z "${REMOTE_FDR}"

  # 全局参数变量
  # 如果环境变量存在，则将其赋值给全局变量
  # 如果环境变量不存在，则设置全局变量为默认值

  # 拉流模式
  #   - pan:   网盘下载
  #   - speed: 测速下载
  #   - file:  文件下载
  [ -z "$SP_MODE" ] && SP_MODE="file"

  # 是否开启调试模式
  [ -z "$SP_DEBUG" ] && SP_DEBUG=true

  [ -z "$SP_REMOTE_CONFIG" ] && SP_REMOTE_CONFIG=false                            # 是否启用远程配置
  [ -z "$TASK_NAME" ] && TASK_NAME="task-Pull-ST-ILL-RT-AAAA-Ex-Prov-js-01"       # 任务名称
  [ -z "$LOCAL_CONF_SERVER" ] && LOCAL_CONF_SERVER='http://192.168.20.253:8500'   # 本地配置文件服务器地址
  [ -z "$REMOTE_CONF_SERVER" ] && REMOTE_CONF_SERVER='http://192.168.20.253:8500' # 远程配置文件服务器地址
  [ -z "$ALIST_SERVER" ] && ALIST_SERVER='https://ak.gforce.cn:5166'              # AList 服务器入口地址
  [ -z "$ALIST_USERNAME" ] && ALIST_USERNAME='admin'                              # AList 登录账号
  [ -z "$ALIST_PASSWORD" ] && ALIST_PASSWORD='AList@AUK'                          # AList 登录密码
  [ -z "$ALIST_PROVIDER" ] && ALIST_PROVIDER="139YUN"                             # 默认网盘存储
  [ -z "$MAX_TIME" ] && MAX_TIME=10                                               # 指定单个传输最长时间(秒)
  [ -z "$MAX_RETRIES" ] && MAX_RETRIES=1                                          # 最大错误重试次数
  [ -z "$RETRY_INTERVAL" ] && RETRY_INTERVAL=2                                    # 重试时间间隔(秒)
  [ -z "$DOWNLOAD_TIMES" ] && DOWNLOAD_TIMES=2                                    # 资源重复下载次数
  [ -z "$ROUND_INTERVAL" ] && ROUND_INTERVAL=5                                    # 多轮运行之间的时间间隔(秒)
  [ -z "$DNS_TIMEOUT" ] && DNS_TIMEOUT=5                                          # DNS 解析超时时间(秒)
  [ -z "$CONNECT_TIMEOUT" ] && CONNECT_TIMEOUT=5                                  # TCP 连接超时时间(秒)
  [ -z "$CORRECTION_FACTOR" ] && CORRECTION_FACTOR=1.00                           # 修正系数，用于补偿其他网络开销
  [ -z "$LIMIT_RATE" ] && LIMIT_RATE=1500000                                      # 下载限速(Bytes/s)
  [ -z "$DNS_TYPE" ] && DNS_TYPE="AAAA"                                           # DNS 解析记录类型 (A/AAAA)
  [ -z "$LOG_SERVER" ] && LOG_SERVER="192.168.120.136:9001"                       # 日志服务器地址
  [ -z "$MAX_REPLICAS" ] && MAX_REPLICAS=128                                      # 最大容器副本数
fi

# 打印服务器返回 JSON 报文中的内容，参数为 jq 筛选字符串
print_resp() {
  # 接收 jq 筛选字符串作为参数
  local file="$1"
  local filter="$2"

  # 让 jq 输出的内容向右缩进 4 个空格
  # 保存原始stdout文件描述符
  exec 3>&1
  # 重定向stdout到临时文件, 输出结果的每一行缩进 4 个空格
  exec 1> >(sed 's/^/    -> /')
  # 注意 jq 需要加上 -C 参数，强制开启颜色转义序列
  jq -r -C "${filter}" "${file}"
  # 恢复stdout
  exec 1>&3
  # 关闭临时文件描述符
  exec 3>&-
}

# 按需截短字符串
truncate_string() {
  local input_string=$1
  local max_length=$2 
  local input_length=${#input_string}

  # 如果输入字符串长度小于等于指定长度，则直接返回
  if ((input_length <= max_length)); then
    echo "$input_string"
    return
  fi

  # 保留开头16个字符
  local start_chars=${input_string:0:16}

  # 保留末尾16个字符
  local end_chars=${input_string: -16}

  # 计算中间需要保留的字符数
  local remaining_length=$((max_length - 32))

  # 截取中间部分
  local middle_chars=${input_string:16:$remaining_length}

  # 输出截短后的字符串
  echo "${start_chars}${middle_chars}...${end_chars}"
}

# 验证 JSON 内容是否合法
validate_json() {
  # Function to validate JSON content
  json_content="$1"
  # Check if the JSON content is valid
  if jq -e . >/dev/null 2>&1 <<<"$json_content"; then
    return 0 # JSON is valid
  else
    return 1 # JSON is invalid
  fi
}

# 从 browser profiles 数据库中随机取出一个符合要求的配置
pick_random_profile() {
  local profileFile=${CONFIG_FDR}/sp_profiles.json
  local tag=$1
  
  # 使用 jq 同时获取 Raw 和 TLSConfig 字段
  if [ -z "$tag" ]; then
    readarray -t records <<<"$(jq -crM '.[]' "$profileFile")"
  else
    readarray -t records <<<"$(jq -crM '.[] | select(.tag | index("'"$tag"'") != null)' "$profileFile")"
  fi
  
  totalProfiles=${#records[@]}
  randomNum=$((RANDOM % totalProfiles))
  
  # 返回 JSON 格式的结果
  echo "${records[$randomNum]}"
}

# 获取当前容器的副本索引
get_replica_index() {
  local lock_file="$STATE_DIR/allocation.lock"
  local counter_file
  local first_run_file

  # 根据 DNS_TYPE 选择不同的计数器文件
  if [ "${DNS_TYPE}" = "AAAA" ]; then
    counter_file="$STATE_DIR/counter_v6"
    first_run_file="$STATE_DIR/.first_run_v6"
  else
    counter_file="$STATE_DIR/counter_v4"
    first_run_file="$STATE_DIR/.first_run_v4"
  fi

  # 确保状态目录存在
  mkdir -p "$STATE_DIR" >/dev/null 2>&1

  # Check if this is first run
  if [ ! -f "$first_run_file" ]; then
    # Clear counter files on first run
    rm -f "$counter_file" >/dev/null 2>&1
    touch "$first_run_file" >/dev/null 2>&1
  fi

  # Use flock for atomic operation
  (
    flock -x 200

    # Assign next index
    local active_replicas
    local replica_index
    active_replicas=$(cat "$counter_file" 2>/dev/null || echo 0)
    replica_index=$((active_replicas + 1))
    
    echo "$replica_index" | tee "$counter_file"
    
  ) 200>"$lock_file"
}

# 解析 hostname 并将相关信息存入对应的字段，并以单行 JSON 格式输出
# 示例调用
# parse_hostname "js-sz-tyl-01-worker-1"
parse_hostname() {
  local hostname="$1"
  
  # 使用 '-' 分割 hostname
  IFS='-' read -r -a parts <<< "$hostname"
  
  # 检查分割后的数组长度是否符合预期
  if [ "${#parts[@]}" -ne 6 ]; then
    sp_log "ERROR" "Invalid hostname format"
    echo "{\"region\":\"-\",\"city\":\"-\",\"branch\":\"-\",\"server\":\"-\",\"role\":\"-\",\"instance\":\"-\"}"
    return 1
  fi
  
  # 将相关信息存入对应的字段
  local region="${parts[0]//-/}"
  local city="${parts[1]//-/}"
  local branch="${parts[2]//-/}"
  local server="${parts[3]//-/}"
  local role="${parts[4]//-/}"
  local instance="${parts[5]//-/}"
  
  # 输出解析结果为单行 JSON 格式
  echo "{\"region\":\"$region\",\"city\":\"$city\",\"branch\":\"$branch\",\"server\":\"$server\",\"role\":\"$role\",\"instance\":\"$instance\"}"
}

# 日志输出函数
sp_log() {
  local level="$1"
  local message="$2"
  
  # 定义颜色代码
  local blue='\033[0;94m'
  local green='\033[0;92m'
  local nocolor='\033[0m'
  
  # DEBUG 级别的日志只在 SP_DEBUG 为 true 时输出
  # 如果开启了调试模式
  if [ "${SP_DEBUG:-false}" = "true" ]; then
    # 调试级别日志，添加蓝色 [DBG] 前缀
    if [ "$level" = "DEBUG" ]; then
      echo -e "${blue}[DBG]${nocolor} $message"
    # INFO 级别日志，添加绿色 [INF] 前缀 
    elif [ "$level" = "INFO" ]; then
      echo -e "${green}[INF]${nocolor} $message"
    fi
  # 如果是普通模式且为 INFO 级别日志
  elif [ "$level" = "INFO" ]; then
    # 如果是 INFO 级别，直接输出, 不打印前缀
    # echo "$message"
    echo -e "${green}[INF]${nocolor} $message"
  fi
}

# 获取本地可用于访问公网的网卡接口 IP
# 返回格式: IP 地址字符串，如果没有找到则返回空字符串
sp_get_local_wan_ip() {
  local ip=""
  
  # 根据 DNS_TYPE 判断获取 IPv4 还是 IPv6 地址
  if [ "${DNS_TYPE}" = "AAAA" ]; then
    # 使用 ifconfig 命令获取IPv6地址, 并随机挑选一个
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
      # 检查 br-lan 接口是否存在
      if ifconfig br-lan >/dev/null 2>&1; then
        # 获取 br-lan 接口上所有剩余时间在 90-120 秒之间的 IPv6 地址
        ipv6_list=$(ip -6 addr show dev br-lan | awk '
          /inet6.*scope global dynamic/ {
            # 提取IPv6地址
            ipv6 = $2; 
            gsub(/\/.*/, "", ipv6);
            
            # 读取下一行
            getline;
            
            # 直接使用正则表达式和gsub提取数字
            if(/valid_lft/) {
              temp = $0;
              gsub(/.*valid_lft /, "", temp);  # 删除valid_lft之前的所有内容
              gsub(/sec.*/, "", temp);         # 删除sec之后的所有内容
              time = int(temp);
              
              #printf "调试: %s -> valid_lft: %d秒", ipv6, time;
              
              if(time > 90 && time <= 120) {
                #printf " [符合条件]\n";
                print ipv6;
              } else {
                #printf " [不符合条件]\n";
              }
            }
          }')
      fi
      
      # 如果 br-lan 不存在或获取失败，则获取所有接口的 IPv6 地址
      if [ -z "$ipv6_list" ]; then
        # 获取所有可用的IPv6地址
        ipv6_list=$(ifconfig | grep -Eo "(2408:|2409:|240e:)([0-9a-fA-F]{1,4}:){0,7}([0-9a-fA-F]{1,4}|:)" | sort -u)
      fi
        
      if [ -n "$ipv6_list" ]; then
        if [ "${REPLICA_INDEX:-0}" -eq 0 ]; then
          # 如果副本索引为0，随机选择一个地址
          ip=$(echo "$ipv6_list" | shuf -n 1)
        else
          # 根据副本索引选择特定的地址, 这种算法相比简单的随机数方法有以下优势:
          # 1. 确定性: 相同副本索引总是得到相同的IP地址,便于问题追踪
          # 2. 均匀分布: 通过取模运算确保IP地址被均匀使用
          # 3. 避免冲突: 不同副本索引会映射到不同的IP地址,减少地址冲突
          total_ips=$(echo "$ipv6_list" | wc -l)
          index=$((REPLICA_INDEX % MAX_REPLICAS % total_ips))
          [ $index -eq 0 ] && index=$total_ips
          ip=$(echo "$ipv6_list" | sed -n "${index}p")
        fi
      fi

    fi
  else
    # IPv4 地址获取方法
    # 尝试方法1: 使用 ip route get 命令获取到特定目标地址的源IP
    if command -v ip >/dev/null 2>&1; then
      ip=$(ip route get ******* 2>/dev/null | grep -o 'src [^ ]*' | sed 's/src //' | head -1)
    fi
    
    # 如果方法1失败，尝试方法2: 使用 ifconfig 命令
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
      ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | grep -v '172.1[6-9]\.' | head -1)
    fi
    
    # 如果方法2失败，尝试方法3: 使用 hostname 命令
    if [ -z "$ip" ] && command -v hostname >/dev/null 2>&1; then
      ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
  fi
  
  # 返回找到的IP地址
  echo "$ip"
}

# 获取本地访问配置服务器的 IP 地址
# 返回格式: IP 地址字符串，如果没有找到则返回空字符串
sp_get_local_lan_ip() {
  local ip=""
  local remote_server=""
  
  # 优先使用本地配置服务器
  if [ -n "$LOCAL_CONF_SERVER" ]; then
      remote_server="$LOCAL_CONF_SERVER"
  else
      remote_server="$REMOTE_CONF_SERVER"
  fi

  # 从服务器URL中提取域名或IP
  local server_host
  server_host=$(echo "$remote_server" | sed -E 's|^https?://||' | sed -E 's|:[0-9]+/?.*$||')

  # IPv4 地址获取
  if command -v ip getent >/dev/null 2>&1 && command -v getent >/dev/null 2>&1; then
      ip=$(ip route get "$(getent ahosts "$server_host" | head -n1 | awk '{print $1}')" 2>/dev/null | grep -o 'src [^ ]*' | sed 's/src //' | head -1)
  elif command -v ifconfig >/dev/null 2>&1; then
      ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | grep -v '172.1[6-9]\.' | head -1)
  else
    echo "  -> Failed to get local LAN IP"
    return 1
  fi

  echo "$ip"
}

# 将 IPv6 地址按段落分级存储到目录中
# 参数:
#   $1: IPv6 地址
#   $2: 基础目录路径(可选,默认为/tmp/ipv6_segments)
# 返回: 无
# 示例:
#   sp_store_ipv6_segments "2408:8756:3a1b:1234:5678:abcd:ef01:2345" "/tmp/custom_dir"
sp_store_ipv6_segments() {
  local ipv6_addr="$1"
  local base_dir="${2:-/tmp/ipv6_segments}"
  
  # 检查输入参数
  if [ -z "$ipv6_addr" ]; then
    sp_log "DEBUG" "IPv6 address is required"
    return 1
  fi
  
  # 创建基础目录
  mkdir -p "$base_dir"
  
  # 将IPv6地址按:分割成数组
  IFS=':' read -ra segments <<< "$ipv6_addr"
  
  # 当前工作目录
  local current_dir="$base_dir"
  
  # 遍历所有段落创建目录结构
  for segment in "${segments[@]}"; do
    # 跳过空段落
    if [ -z "$segment" ]; then
      continue
    fi
    
    # 创建当前段落对应的目录
    current_dir="$current_dir/$segment"
    mkdir -p "$current_dir"
    
    # 在每一级目录下创建一个标记文件，记录完整的IPv6地址
    echo "$ipv6_addr" > "$current_dir/.ipv6_addr"
  done
  
  sp_log "DEBUG" "IPv6 address $ipv6_addr has been stored in directory structure"
}

# 检查 IPv6 地址是否已经存在于目录结构中
# 参数:
#   $1: IPv6 地址
#   $2: 基础目录路径(可选,默认为/tmp/ipv6_segments)
# 返回: 
#   0: 地址存在
#   1: 地址不存在
#   2: 参数错误
# 示例:
#   sp_check_ipv6_exists "2408:8756:3a1b:1234:5678:abcd:ef01:2345" "/tmp/custom_dir"
sp_check_ipv6_exists() {
  local ipv6_addr="$1"
  local base_dir="${2:-/tmp/ipv6_segments}"
  
  # 检查输入参数
  if [ -z "$ipv6_addr" ]; then
    sp_log "DEBUG" "IPv6 address is required"
    return 2
  fi
  
  # 检查基础目录是否存在
  if [ ! -d "$base_dir" ]; then
    sp_log "DEBUG" "Base directory does not exist"
    return 1
  }
  
  # 将IPv6地址按:分割成数组
  IFS=':' read -ra segments <<< "$ipv6_addr"
  
  # 当前工作目录
  local current_dir="$base_dir"
  
  # 遍历所有段落检查目录结构
  for segment in "${segments[@]}"; do
    # 跳过空段落
    if [ -z "$segment" ]; then
      continue
    fi
    
    # 检查当前段落对应的目录是否存在
    current_dir="$current_dir/$segment"
    if [ ! -d "$current_dir" ]; then
      return 1
    fi
  done
  
  # 检查最终目录中的.ipv6_addr文件是否存在且内容匹配
  if [ -f "$current_dir/.ipv6_addr" ]; then
    local stored_addr
    stored_addr=$(cat "$current_dir/.ipv6_addr")
    if [ "$stored_addr" = "$ipv6_addr" ]; then
      sp_log "DEBUG" "IPv6 address $ipv6_addr exists in directory structure"
      return 0
    fi
  fi
  
  return 1
}

# 输出完成信息
sp_done() {
  echo "==> Done"
}

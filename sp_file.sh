#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh
source utils/sp_dns.sh
source utils/sp_sync.sh

# 定义关联数组用来存放 URL 配置文件路径
declare -A url_conf_files=(
  ["file"]="${CONFIG_FDR}/sp_url.json"
  ["pan"]="${CONFIG_FDR}/sp_pan.json"
)

# 读取配置文件, 随机返回文件下载 URL
sp_getURL() {
  local mode="$1"

  # 使用 jq 进行筛选并统计数量
  jsonText=$(jq -crM "." "${url_conf_files[$mode]}")

  # 获取配置中 URL 的总个数
  totalURLs=$(jq 'length' <<<"$jsonText")

  if [ "$totalURLs" -le 0 ]; then
    echo ""
    return
  fi

  # 从 URL 列表中，随机挑选一个
  random_number=$((RANDOM % totalURLs))
  record=$(jq -crM ".[${random_number}]" <<<"$jsonText")

  # 提取出 url, tag 等字段
  url=$(echo "$record" | jq -rM '.url')
  tag=$(echo "$record" | jq -rM '.tag')
  referer=$(echo "$record" | jq -rM '.referer')

  echo '{"url": "'"$url"'", "tag": "'"$tag"'", "referer": "'"$referer"'"}'
}

# 向服务器发送下载请求
#
# 说明：
#    - 参数1: 下载模式, 分为 file 和 pan 两种
# 调用示例：
#    sp_url_download "file"
sp_url_download() {
  sp_log "DEBUG" "==> Downloading files... "

  local mode="$1"

  [ -z "${mode}" ] && mode="file"

  # 解析 hostname
  NODE_INFO=$(parse_hostname "$(uname -n)")
  NODE_REGION=$(jq -crM '.region' <<<"$NODE_INFO")
  NODE_CITY=$(jq -crM '.city' <<<"$NODE_INFO")
  NODE_BRANCH=$(jq -crM '.branch' <<<"$NODE_INFO")
  NODE_SERVER=$(jq -crM '.server' <<<"$NODE_INFO")
  NODE_INSTANCE=$(jq -crM '.instance' <<<"$NODE_INFO")

  # 获取 URL
  record=$(sp_getURL "${mode}")

  if [ -z "$record" ]; then
    sp_log "DEBUG" "  -> Invalid URL: ${record}"
    return
  fi

  # 提取 URL
  URL=$(jq -rM '.url' <<<"$record")

  # 检查 URL 是否为空或为 "null"
  if [ -z "$URL" ] || [ "$URL" = "null" ]; then
    sp_log "DEBUG" "  -> Invalid URL: empty or null"
    return
  fi

  # 提取前缀
  PREFIX=$(awk -F[/:] '{print $1"://"}' <<<"$URL")
  # 提取域名
  DOMAIN=$(awk -F[/:] '{print $4}' <<<"$URL")
  # 提取 URI
  # URI=$(sed 's|^.*://[^/]*||g' <<<"$URL")
  # 去除协议和主机部分，只保留路径
  URI="${URL#*://}"
  URI="/${URI#*/}"
  # 提取 referer
  REFERER=$(jq -rM '.referer' <<<"$record")
  # 根据域名随机选择 Browser Profile Tag
  TAG=$(jq -rM '.tag' <<<"$record")
  
  PROFILE=$(pick_random_profile "${TAG}")
  COMMAND=$(echo "$PROFILE" | jq -r '.command')
  CIPHERS=$(echo "$PROFILE" | jq -r '.ciphers')

  # 构建 curl 命令的基础部分
  CURL_ARGS=()
  CURL_ARGS+=("$COMMAND")
  CURL_ARGS+=("--ciphers" "$CIPHERS")

  # 添加 headers
  while IFS= read -r header_line; do
    CURL_ARGS+=(-H "$header_line")
  done < <(echo "$PROFILE" | jq -r '.headers | to_entries[] | "\(.key): \(.value)"')

  # 添加 options
  while IFS= read -r option_line; do
    for option in $option_line; do
      [[ -n "$option" ]] && CURL_ARGS+=("$option")
    done
  done < <(echo "$PROFILE" | jq -r '.options[]')

  # 提取端口
  REMOTE_PORT=$(echo "$URL" | awk -F: '{print $3}' | awk -F/ '{print $1}')
  # 如果提取的端口为空，则使用默认端口
  if [ -z "$REMOTE_PORT" ]; then
    if [ "$PREFIX" = "http://" ]; then
      REMOTE_PORT="80"
    else
      REMOTE_PORT="443"
    fi
  fi

  sp_log "DEBUG" "  -> URL: $URL"

  # 在本地数据库中查询域名, 获取 IP 和地理信息记录
  rr=$(sp_lookup "${DOMAIN}" "${DNS_TYPE}")

  # 返回内容为空 (在当前筛选条件下没有符合条件的记录)
  if [ "${rr}" = "{\"\"}" ]; then
    sp_log "DEBUG" "==> no records meet the current selection criteria: ${DOMAIN}"
    return
  elif [ "${rr}" = "{}" ]; then
    sp_log "DEBUG" "==> no records: ${DOMAIN}"
    return
  fi

  # 提取解析 IP
  REMOTE_IP=$(jq -crM '.data' <<<"$rr")
  # 提取 IP 地理归属信息
  LOC=$(jq -crM '.location' <<<"$rr")
  # 提取 服务端 类型(serviceType)
  SERVICE_TYPE=$(jq -crM '.serviceType' <<<"$rr")
  # 提取 提供商
  PROVIDER=$(jq -crM '.provider' <<<"$rr")

  # 创建临时文件
  TEMP_FILE=$(mktemp)

  # 创建 curl 格式文件
  cat > "$TEMP_FILE" <<EOF
    local_ip:  %{local_ip}\n
    local_port:  %{local_port}\n
    remote_ip:  %{remote_ip}\n
    remote_port:  %{remote_port}\n
    time_namelookup:  %{time_namelookup}\n
    time_connect:  %{time_connect}\n
    time_total:  %{time_total}\n
    speed_download:  %{speed_download}\n
    size_request:  %{size_request}\n
    size_download:  %{size_download}\n
    size_header:  %{size_header}\n
    http_code:  %{http_code}\n
    url_effective:  %{url_effective}\n
EOF

  # 添加通用 curl 参数
  CURL_ARGS+=(
    "--insecure"
    # "--verbose"
    "--silent"
    "--retry" "0"
    "--connect-timeout" "${CONNECT_TIMEOUT}"
    "--max-time" "${MAX_TIME}"
    "--limit-rate" "${LIMIT_RATE}"
    ${REFERER:+"--referer" "${REFERER}"}
    "--header" "Host: ${DOMAIN}"
    "--max-redirs" "0"
    "--output" "/dev/null"
  )

  if [[ "$REMOTE_IP" =~ ":" ]]; then
    IP_VERSION="IPv6"
  else
    IP_VERSION="IPv4"
  fi

  sp_log "DEBUG" "  -> IP: ${IP} LOC: ${LOC} [${SERVICE_TYPE}] (${PROVIDER})"

  for ((t = 1; t <= DOWNLOAD_TIMES; t++)); do
    sp_log "DEBUG" "  -> Downloading ( $t of $DOWNLOAD_TIMES ) ... "

    # 获取本地可用于访问公网的 IP
    LOCAL_IP=$(sp_get_local_wan_ip)

    # 检查 LOCAL_IP 是否未被赋值或为空字符串
    if [ -z "${LOCAL_IP// }" ]; then
      sp_log "DEBUG" "  -> No local IP found, exiting..."
      return 1
    fi
    
    # 重置 curl 参数数组，避免参数累积
    CURL_DOWNLOAD_ARGS=("${CURL_ARGS[@]}")
    
    # 构建 curl 运行参数
    # 检查是否为 IPv6 地址
    if [[ "$REMOTE_IP" =~ ":" ]]; then
      # IPv6 地址需要用方括号括起来， 使用随机本地网卡接口 IP 访问
      CURL_DOWNLOAD_ARGS+=(
        "-w" "@$TEMP_FILE"
        "--interface" "${LOCAL_IP}"
        "--resolve" "${DOMAIN}:${REMOTE_PORT}:[${REMOTE_IP}]"
        "${URL}"
      )
    else
      # IPv4 地址直接使用
      CURL_DOWNLOAD_ARGS+=(
        "-w" "@$TEMP_FILE"
        "--resolve" "${DOMAIN}:${REMOTE_PORT}:${REMOTE_IP}"
        "${URL}"
      )
    fi

    sp_log "DEBUG" "  -> curl_cmd: ${CURL_DOWNLOAD_ARGS[*]}"

    # 开始下载, 并限制下载总时长
    "${CURL_DOWNLOAD_ARGS[@]}" > "${TEMP_FILE}.out" 2>&1

    # 提取结果
    LOCAL_PORT=$(grep "local_port:" "$TEMP_FILE.out" | awk '{print $2}'| tr -dc '0-9\n' | head -n 1)
    TIME_TOTAL=$(grep "time_total:" "$TEMP_FILE.out" | awk '{print $2}'| tr -dc '0-9\n' | head -n 1)
    SIZE_HEADER=$(grep "size_header:" "$TEMP_FILE.out" | awk '{print $2}' | tr -dc '0-9\n' | head -n 1)
    SIZE_DOWNLOAD=$(grep "size_download:" "$TEMP_FILE.out" | awk '{print $2}' | tr -dc '0-9\n' | head -n 1)
    # 确保变量不为空，如果为空则默认为0
    SIZE_HEADER=${SIZE_HEADER:-0}
    SIZE_DOWNLOAD=${SIZE_DOWNLOAD:-0}
    TOTAL_DOWNLOAD=$((SIZE_DOWNLOAD + SIZE_HEADER))
    HTTP_CODE=$(grep "http_code:" "$TEMP_FILE.out" | awk '{print $2}')
    URL_EFFECTIVE=$(grep "url_effective:" "$TEMP_FILE.out" | awk '{print $2}')

    # 有效性检查
    # 检查 LOCAL_PORT 是否未被赋值或为空字符串，或不在正常端口范围(1-65535)
    if [ -z "${LOCAL_PORT// }" ] || ! [[ "$LOCAL_PORT" =~ ^[0-9]+$ ]] || [ "$LOCAL_PORT" -lt 1 ] || [ "$LOCAL_PORT" -gt 65535 ]; then
        LOCAL_PORT=0
    fi
    # 如果 HTTP_CODE 不是合法的数字或者为 000，则将其赋值为 0
    if ! [[ "$HTTP_CODE" =~ ^[0-9]+$ ]] || [ "$HTTP_CODE" = "000" ]; then
      HTTP_CODE=0
    fi
    # 检查 URL_EFFECTIVE 是否为空字符串
    if [ -z "${URL_EFFECTIVE// }" ]; then
      URL_EFFECTIVE=$URL
    fi
    # 如果 TOTAL_DOWNLOAD 为 0，则 SPEED_DOWNLOAD 赋值为 0
    if [ "$TOTAL_DOWNLOAD" -eq 0 ]; then
      SPEED_DOWNLOAD=0
    else
      SPEED_DOWNLOAD=$(grep "speed_download:" "$TEMP_FILE.out" | awk '{print $2}' | tr -dc '0-9\n' | head -n 1)
    fi
    # 如果 HTTP_CODE 不为 0, TOTAL_DOWNLOAD 还需要加上协议开销
    if [ "$HTTP_CODE" -ne 0 ]; then
        # MTU通常为1500字节，减去IP头和TCP头后的有效载荷约为1460字节(IPv4)或1440字节(IPv6)
        if [ "$IP_VERSION" = "IPv6" ]; then
            MSS=1440
            HEADER_SIZE=$((14 + 40 + 20 + 4))  # 以太网头(14) + IPv6头(40) + TCP头(20) + FCS(4)
        else
            MSS=1460
            HEADER_SIZE=$((14 + 20 + 20 + 4))  # 以太网头(14) + IPv4头(20) + TCP头(20) + FCS(4)
        fi
        
        # 计算需要多少个完整包(向上取整)
        FULL_PACKETS=$(( (TOTAL_DOWNLOAD + MSS - 1) / MSS ))
        
        # 计算总的协议开销
        PROTOCOL_OVERHEAD=$((FULL_PACKETS * HEADER_SIZE))
        
        # 加上协议开销得到物理层总字节数
        # 计算最终的总下载量，包含协议开销和修正系数，并取整
        TOTAL_DOWNLOAD=$(echo "($TOTAL_DOWNLOAD + $PROTOCOL_OVERHEAD) * $CORRECTION_FACTOR" | bc | cut -d'.' -f1)

        # 这样计算出的 TOTAL_DOWNLOAD 会更接近实际在物理网口传输的字节数。不过请注意，这仍然是一个估算值，因为：
        # 1. 实际传输中可能会有TCP重传
        # 2. TCP的确认包(ACK)也会产生额外开销
        # 3. 可能有TCP慢启动和拥塞控制的影响
        # 4. 实际的MTU可能因网络路径而异
    fi

    # 输出结果
    GEOIP_COUNTRY=$(jq -crM '.country' <<<"$LOC")
    GEOIP_PROVINCE=$(jq -crM '.province' <<<"$LOC")
    GEOIP_CITY=$(jq -crM '.city' <<<"$LOC")
    GEOIP_ISP=$(jq -crM '.isp' <<<"$LOC")

    # 构建日志内容
    message="$NODE_REGION,$NODE_CITY,$NODE_BRANCH,$NODE_SERVER,$NODE_INSTANCE,$LOCAL_IP,$LOCAL_PORT,$REMOTE_IP,$REMOTE_PORT,$IP_VERSION,$SERVICE_TYPE,$PROVIDER,$GEOIP_COUNTRY,$GEOIP_PROVINCE,$GEOIP_CITY,$GEOIP_ISP,$HTTP_CODE,$TIME_TOTAL,$SPEED_DOWNLOAD,$TOTAL_DOWNLOAD,$TASK_NAME,$DOMAIN,$URL_EFFECTIVE"

    # 输出结果至 console  
    sp_log "INFO" "$message"

    # 通过 nc 命令将日志内容推送至 filebeat 的 UDP 端口
    # Parse LOG_SERVER into host and port
    FILEBEAT_HOST=$(echo "$LOG_SERVER" | cut -d':' -f1)
    FILEBEAT_PORT=$(echo "$LOG_SERVER" | cut -d':' -f2)
    echo -n "$message" | nc -u -w1 "$FILEBEAT_HOST" "$FILEBEAT_PORT"

    # 检查 HTTP 状态码不是 200 或者下载速度过低
    # Check if HTTP code is not 200 or download speed is too low
    if [ "$HTTP_CODE" -ne 200 ]; then
      return 1
    elif [ "$(echo "$SPEED_DOWNLOAD < ${LIMIT_RATE}/3" | bc -l)" -eq 1 ]; then
      return 2
    fi
  done

  return 0
}

# 从配置服务器拉取任务配置
#
# 说明：
#   - 优先从本地配置缓存服务器拉取
#   - 本地服务器失败时尝试从远程配置服务器拉取
sp_get_remote_task_config() {
  sp_log "DEBUG" "==> Pulling task config from local server"
  if ! sp_pullTaskConfig "$LOCAL_CONF_SERVER"; then
    sp_log "DEBUG" "Failed to pull task config from local server, exiting..."
    exit 1
  fi

  if [ -n "$LOCAL_CONF_SERVER" ]; then
    sp_log "DEBUG" "==> Pulling task from local server"
    sp_pullTask "$LOCAL_CONF_SERVER" "$TASK_NAME" "$CONFIG_FDR"
    retcode=$?
    if [ $retcode -ne 0 ]; then
      sp_log "DEBUG" "Failed to pull task from local server, exiting..."
      exit 1
    fi
  else
    sp_log "DEBUG" "Pulling task from remote server"
    sp_pullTask "$REMOTE_CONF_SERVER" "$TASK_NAME" "$CONFIG_FDR"
    retcode=$?
    if [ $retcode -ne 0 ]; then
      sp_log "DEBUG" "Failed to pull task from remote server, exiting..."
      exit 1
    fi
  fi
}

# 判断是否是 docker 环境，如果是 docker 环境，则获取副本编号
[ -e /.dockerenv ] && REPLICA_INDEX=$(get_replica_index)

REPLICA_INDEX=${REPLICA_INDEX:-0}  # Set default value if empty

if [ "$REPLICA_INDEX" -eq 0 ]; then
  sp_log "DEBUG" "==> Unable to get valid replica index"
else
  sp_log "DEBUG" "==> Replica index: $REPLICA_INDEX of $MAX_REPLICAS"
fi

while true; do
  # 根据环境变量判断是否拉取远程配置, 默认为 true (启用)
  if [ "${SP_REMOTE_CONFIG:-true}" = "true" ]; then
    sp_get_remote_task_config
  else
    sp_log "DEBUG" "==> Remote configuration is disabled, skipping..."
  fi

  # 下载拉流
  # 重试下载，最多重试 MAX_RETRIES 次
  retries=0
  while [ "$retries" -lt "$MAX_RETRIES" ]; do
    if sp_url_download "$1"; then
      sp_log "DEBUG" "  -> Download successful"
      break
    fi
    
    retries=$((retries + 1))
    if [ "$retries" -lt "$MAX_RETRIES" ]; then
      sp_log "DEBUG" "  -> Download failed, retrying in $RETRY_INTERVAL seconds... (Attempt $((retries + 1))/$MAX_RETRIES)"
      sleep "$RETRY_INTERVAL"
    else
      sp_log "DEBUG" "  -> Download failed after $MAX_RETRIES attempts"
      break
    fi
  done

  # 完成
  sp_log "DEBUG" "$(sp_done)"
done

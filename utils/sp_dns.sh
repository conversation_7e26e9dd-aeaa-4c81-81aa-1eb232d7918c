#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh

dnsConfFile=${CONFIG_FDR}/sp_dns.json

# 域名查找函数
#
# 说明：
#    - 返回结果为 JSON 格式，包括有 type, data, location 等字段
#    - 如果查找不到结果，则返回空字符串
# 参数说明：
#    - $1: 查询的域名（必选）
#    - $2: 查询的类型（可选）
#      - A: IPv4 记录
#      - AAAA: IPv6 记录
#      - 缺省: 不筛选类型（IPv4 或者 IPv6）
# 调用示例：
#    sp_lookup "d.musicapp.migu.cn" A
# 返回示例：
#    {"type":"A","data":"**************","location":"中国广东广州 移动"}
sp_lookup() {
  # 获取待查域名
  domain=$1
  type=$2

  r=$(jq -crM '."'"${domain}"'"' "${dnsConfFile}")

  # 域名没有收录
  if [ "$r" = "null" ]; then
    echo "{}"
    return
  fi

  # 从配置文件中查找域名
  if [ "$type" ]; then
    r=$(jq -crM '. | map(select(.type=="CNAME" or .type=="'"${type}"'"))' <<<"$r")
  else
    r=$(jq -crM '. | map(select(.type=="CNAME" or .type=="A" or .type=="AAAA"))' <<<"$r")
  fi

  if [ -n "$r" ] && [ "$r" != "null" ]; then
    t=$(jq -crM '.[0].type' <<<"$r")

    if [ "$t" = "CNAME" ]; then
      # 如果是 CNAME 记录，则递归查询
      # 随机返回一个结果
      n=$(jq -r 'length' <<<"$r")

      random_number=$((RANDOM % n))
      domain=$(jq -crM '.['${random_number}'].data' <<<"$r")
      sp_lookup "${domain}" "${type}"
    else
      # 如果是 A 或者 AAAA 记录

      # 随机返回一个结果
      n=$(jq -r 'length' <<<"$r")

      if [ "$n" -gt 0 ]; then
        random_number=$((RANDOM % n))
        jq -crM '.['${random_number}']' <<<"$r"
      else
        # 域名有收录, 但都不满足筛选条件
        echo "{\"\"}"
      fi
    fi
  else
    echo "{}"
  fi
}
